"""
ScreenMonitorMCP Web Extension Module v2.0

Modern, refactored web extension module with clean architecture,
proper error handling, validation, and type safety.

This module provides browser extension integration for ScreenMonitorMCP
while maintaining backward compatibility with the existing system.
"""

from typing import Dict, List, Optional, Any
import os
import logging
from dataclasses import dataclass
from enum import Enum

__version__ = "2.0.0"
__author__ = "ScreenMonitorMCP Team"

# ============================================================================
# Configuration Classes
# ============================================================================

class ExtensionFeature(Enum):
    """Available extension features"""
    WEB_MONITORING = "web_monitoring"
    DOM_DETECTION = "dom_detection"
    WEB_SMART_CLICK = "web_smart_click"
    DOMAIN_WHITELIST = "domain_whitelist"
    WEB_AUTOMATION = "web_automation"
    EVENT_ANALYSIS = "event_analysis"

@dataclass
class ExtensionConfig:
    """Extension configuration data class"""
    enabled: bool = False
    features: Dict[ExtensionFeature, bool] = None
    allowed_domains: List[str] = None
    api_key: str = ""
    version: str = __version__
    debug_mode: bool = False
    
    def __post_init__(self):
        if self.features is None:
            self.features = {
                ExtensionFeature.WEB_MONITORING: True,
                ExtensionFeature.DOM_DETECTION: True,
                ExtensionFeature.WEB_SMART_CLICK: True,
                ExtensionFeature.DOMAIN_WHITELIST: True,
                ExtensionFeature.WEB_AUTOMATION: True,
                ExtensionFeature.EVENT_ANALYSIS: True,
            }
        
        if self.allowed_domains is None:
            self.allowed_domains = ["localhost", "127.0.0.1", "github.com", "stackoverflow.com"]

# ============================================================================
# Configuration Management
# ============================================================================

def _load_config_from_env() -> ExtensionConfig:
    """Load configuration from environment variables"""
    enabled = os.getenv("ENABLE_WEB_EXTENSION", "false").lower() == "true"
    debug_mode = os.getenv("WEB_EXTENSION_DEBUG", "false").lower() == "true"
    
    # Parse allowed domains
    domains_str = os.getenv("ALLOWED_DOMAINS", "localhost,127.0.0.1,github.com,stackoverflow.com")
    domains = [domain.strip() for domain in domains_str.split(",") if domain.strip()]
    
    # Ensure essential domains are included
    essential_domains = ["localhost", "127.0.0.1", "file"]
    for domain in essential_domains:
        if domain not in domains:
            domains.append(domain)
    
    # Parse features
    features = {}
    for feature in ExtensionFeature:
        env_key = f"FEATURE_{feature.value.upper()}"
        features[feature] = os.getenv(env_key, "true").lower() == "true"
    
    return ExtensionConfig(
        enabled=enabled,
        features=features,
        allowed_domains=domains,
        api_key=os.getenv("WEB_EXTENSION_API_KEY", ""),
        debug_mode=debug_mode
    )

# Global configuration instance
_config: Optional[ExtensionConfig] = None

def get_config() -> ExtensionConfig:
    """Get current extension configuration"""
    global _config
    if _config is None:
        _config = _load_config_from_env()
    return _config

def reload_config() -> ExtensionConfig:
    """Reload configuration from environment"""
    global _config
    _config = _load_config_from_env()
    return _config

# ============================================================================
# Public API Functions (Backward Compatibility)
# ============================================================================

def is_extension_enabled() -> bool:
    """Check if web extension features are enabled"""
    return get_config().enabled

def get_enabled_features() -> Dict[str, bool]:
    """Get currently enabled extension features (backward compatible format)"""
    config = get_config()
    if not config.enabled:
        return {}
    
    return {feature.value: enabled for feature, enabled in config.features.items() if enabled}

def get_allowed_domains() -> List[str]:
    """Get allowed domains from configuration"""
    return get_config().allowed_domains.copy()

def is_domain_allowed(domain: str) -> bool:
    """Check if domain is in whitelist"""
    if not domain:
        return False
    
    config = get_config()
    if not config.enabled:
        return False
    
    allowed = config.allowed_domains
    
    # Direct match
    if domain in allowed:
        return True
    
    # Subdomain check
    for allowed_domain in allowed:
        if domain.endswith('.' + allowed_domain):
            return True
    
    return False

def get_extension_config() -> Dict[str, Any]:
    """Get complete extension configuration (backward compatible format)"""
    config = get_config()
    
    return {
        "enabled": config.enabled,
        "features": get_enabled_features(),
        "allowed_domains": config.allowed_domains,
        "api_key": config.api_key,
        "version": config.version,
        "debug_mode": config.debug_mode
    }

# ============================================================================
# Feature Checks
# ============================================================================

def is_feature_enabled(feature: ExtensionFeature) -> bool:
    """Check if a specific feature is enabled"""
    config = get_config()
    return config.enabled and config.features.get(feature, False)

def get_feature_status() -> Dict[ExtensionFeature, bool]:
    """Get status of all features"""
    config = get_config()
    if not config.enabled:
        return {feature: False for feature in ExtensionFeature}
    
    return config.features.copy()

# ============================================================================
# Validation Functions
# ============================================================================

def validate_domain(domain: str) -> bool:
    """Validate domain format"""
    if not domain or not isinstance(domain, str):
        return False
    
    # Basic domain validation
    domain = domain.strip().lower()
    
    # Remove protocol if present
    if domain.startswith(('http://', 'https://')):
        domain = domain.split('://', 1)[1]
    
    # Remove path if present
    domain = domain.split('/')[0]
    
    # Check for invalid characters
    if any(char in domain for char in [' ', '\t', '\n', '\r']):
        return False
    
    # Special case for file protocol
    if domain == 'file':
        return True
    
    # Basic domain format check
    if '.' not in domain and domain not in ['localhost', '127.0.0.1']:
        return False
    
    return True

def validate_session_id(session_id: str) -> bool:
    """Validate session ID format"""
    if not session_id or not isinstance(session_id, str):
        return False
    
    # Basic session ID validation
    return len(session_id) >= 10 and session_id.replace('_', '').replace('-', '').isalnum()

# ============================================================================
# Logging Setup
# ============================================================================

def setup_extension_logging() -> logging.Logger:
    """Setup logging for web extension module"""
    logger = logging.getLogger('web_extension_v2')
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '[%(name)s] %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    config = get_config()
    logger.setLevel(logging.DEBUG if config.debug_mode else logging.INFO)
    
    return logger

# ============================================================================
# Module Initialization
# ============================================================================

# Initialize logging
logger = setup_extension_logging()

# Log initialization
if is_extension_enabled():
    logger.info(f"Web Extension v{__version__} initialized - ENABLED")
    logger.info(f"Enabled features: {list(get_enabled_features().keys())}")
    logger.info(f"Allowed domains: {len(get_allowed_domains())} domains")
else:
    logger.info(f"Web Extension v{__version__} initialized - DISABLED")

# ============================================================================
# Export Public API
# ============================================================================

__all__ = [
    # Configuration
    'ExtensionConfig',
    'ExtensionFeature',
    'get_config',
    'reload_config',
    
    # Backward compatibility functions
    'is_extension_enabled',
    'get_enabled_features',
    'get_allowed_domains',
    'is_domain_allowed',
    'get_extension_config',
    
    # Feature management
    'is_feature_enabled',
    'get_feature_status',
    
    # Validation
    'validate_domain',
    'validate_session_id',
    
    # Logging
    'setup_extension_logging',
    'logger',
    
    # Constants
    '__version__',
]