"""
Web Extension Security Module

Security utilities for the Python web extension server including
input validation, rate limiting, and secure communication.
"""

import hashlib
import hmac
import secrets
import time
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from functools import wraps
import logging
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

# ============================================================================
# Security Configuration
# ============================================================================

@dataclass
class SecurityConfig:
    """Security configuration for web extension server"""
    enable_rate_limiting: bool = True
    enable_input_validation: bool = True
    enable_csrf_protection: bool = True
    enable_encryption: bool = False  # For future implementation
    max_request_size: int = 1024 * 1024  # 1MB
    rate_limit_requests: int = 100
    rate_limit_window: int = 60  # seconds
    session_timeout: int = 1800  # 30 minutes
    allowed_origins: List[str] = field(default_factory=lambda: [
        'chrome-extension://',
        'moz-extension://',
        'http://localhost',
        'https://localhost'
    ])
    trusted_domains: List[str] = field(default_factory=lambda: [
        'localhost',
        '127.0.0.1',
        'file'
    ])

# ============================================================================
# Input Validation
# ============================================================================

class InputValidator:
    """Comprehensive input validation for web extension requests"""
    
    # Regex patterns for validation
    DOMAIN_PATTERN = re.compile(r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$')
    SESSION_ID_PATTERN = re.compile(r'^[a-zA-Z0-9_-]{10,50}$')
    URL_PATTERN = re.compile(r'^https?://[^\s/$.?#].[^\s]*$')
    CSS_SELECTOR_PATTERN = re.compile(r'^[a-zA-Z0-9\s\.\#\[\]\:\-_,>+~=\'"()]+$')
    
    # XSS patterns to detect and block
    XSS_PATTERNS = [
        re.compile(r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>', re.IGNORECASE),
        re.compile(r'javascript:', re.IGNORECASE),
        re.compile(r'on\w+\s*=', re.IGNORECASE),
        re.compile(r'<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>', re.IGNORECASE),
        re.compile(r'<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>', re.IGNORECASE),
        re.compile(r'<embed\b[^<]*>', re.IGNORECASE),
        re.compile(r'data:text\/html', re.IGNORECASE),
        re.compile(r'vbscript:', re.IGNORECASE),
    ]
    
    @classmethod
    def validate_domain(cls, domain: str) -> Tuple[bool, Optional[str]]:
        """Validate domain format"""
        if not domain or not isinstance(domain, str):
            return False, "Domain must be a non-empty string"
        
        domain = domain.strip().lower()
        
        # Special cases
        if domain in ['localhost', '127.0.0.1', 'file']:
            return True, None
        
        # Remove protocol if present
        if domain.startswith(('http://', 'https://')):
            domain = domain.split('://', 1)[1]
        
        # Remove path if present
        domain = domain.split('/')[0]
        
        # Check length
        if len(domain) > 253:
            return False, "Domain name too long"
        
        # Validate format
        if not cls.DOMAIN_PATTERN.match(domain):
            return False, "Invalid domain format"
        
        return True, None
    
    @classmethod
    def validate_session_id(cls, session_id: str) -> Tuple[bool, Optional[str]]:
        """Validate session ID format"""
        if not session_id or not isinstance(session_id, str):
            return False, "Session ID must be a non-empty string"
        
        if not cls.SESSION_ID_PATTERN.match(session_id):
            return False, "Invalid session ID format"
        
        return True, None
    
    @classmethod
    def validate_url(cls, url: str) -> Tuple[bool, Optional[str]]:
        """Validate URL format"""
        if not url or not isinstance(url, str):
            return False, "URL must be a non-empty string"
        
        # Special handling for file:// protocol
        if url.startswith('file://'):
            return True, None
        
        # Validate HTTP/HTTPS URLs
        if not cls.URL_PATTERN.match(url):
            return False, "Invalid URL format"
        
        # Check for XSS patterns
        if cls.contains_xss(url):
            return False, "URL contains potentially malicious content"
        
        return True, None
    
    @classmethod
    def validate_css_selector(cls, selector: str) -> Tuple[bool, Optional[str]]:
        """Validate CSS selector format"""
        if not selector or not isinstance(selector, str):
            return False, "CSS selector must be a non-empty string"
        
        if not cls.CSS_SELECTOR_PATTERN.match(selector):
            return False, "Invalid CSS selector format"
        
        if cls.contains_xss(selector):
            return False, "CSS selector contains potentially malicious content"
        
        return True, None
    
    @classmethod
    def validate_features(cls, features: List[str]) -> Tuple[bool, Optional[str]]:
        """Validate feature list"""
        if not isinstance(features, list):
            return False, "Features must be a list"
        
        valid_features = {
            'dom_monitoring',
            'smart_click',
            'text_extraction',
            'event_analysis',
            'form_automation',
            'navigation_tracking'
        }
        
        for feature in features:
            if not isinstance(feature, str) or feature not in valid_features:
                return False, f"Invalid feature: {feature}"
        
        return True, None
    
    @classmethod
    def contains_xss(cls, text: str) -> bool:
        """Check if text contains XSS patterns"""
        return any(pattern.search(text) for pattern in cls.XSS_PATTERNS)
    
    @classmethod
    def sanitize_string(cls, text: str) -> str:
        """Sanitize string by removing XSS patterns and encoding HTML"""
        if not isinstance(text, str):
            return str(text)
        
        # Remove XSS patterns
        sanitized = text
        for pattern in cls.XSS_PATTERNS:
            sanitized = pattern.sub('', sanitized)
        
        # HTML encode special characters
        sanitized = (sanitized
                    .replace('&', '&amp;')
                    .replace('<', '&lt;')
                    .replace('>', '&gt;')
                    .replace('"', '&quot;')
                    .replace("'", '&#x27;')
                    .replace('/', '&#x2F;'))
        
        return sanitized
    
    @classmethod
    def validate_request_data(cls, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate complete request data"""
        errors = []
        
        # Validate domain if present
        if 'domain' in data:
            valid, error = cls.validate_domain(data['domain'])
            if not valid:
                errors.append(f"Domain validation failed: {error}")
        
        # Validate session_id if present
        if 'session_id' in data:
            valid, error = cls.validate_session_id(data['session_id'])
            if not valid:
                errors.append(f"Session ID validation failed: {error}")
        
        # Validate URL if present
        if 'url' in data:
            valid, error = cls.validate_url(data['url'])
            if not valid:
                errors.append(f"URL validation failed: {error}")
        
        # Validate features if present
        if 'features' in data:
            valid, error = cls.validate_features(data['features'])
            if not valid:
                errors.append(f"Features validation failed: {error}")
        
        # Validate CSS selector if present
        if 'css_selector' in data:
            valid, error = cls.validate_css_selector(data['css_selector'])
            if not valid:
                errors.append(f"CSS selector validation failed: {error}")
        
        return len(errors) == 0, errors

# ============================================================================
# Rate Limiting
# ============================================================================

class RateLimiter:
    """Rate limiting implementation for API endpoints"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests: Dict[str, List[float]] = {}
    
    def is_allowed(self, identifier: str) -> bool:
        """Check if request is allowed for identifier"""
        now = time.time()
        
        # Get existing requests for this identifier
        if identifier not in self.requests:
            self.requests[identifier] = []
        
        requests = self.requests[identifier]
        
        # Remove old requests outside the window
        cutoff = now - self.window_seconds
        self.requests[identifier] = [req_time for req_time in requests if req_time > cutoff]
        
        # Check if under limit
        if len(self.requests[identifier]) >= self.max_requests:
            return False
        
        # Add current request
        self.requests[identifier].append(now)
        return True
    
    def get_remaining_requests(self, identifier: str) -> int:
        """Get remaining requests for identifier"""
        if identifier not in self.requests:
            return self.max_requests
        
        now = time.time()
        cutoff = now - self.window_seconds
        valid_requests = [req_time for req_time in self.requests[identifier] if req_time > cutoff]
        
        return max(0, self.max_requests - len(valid_requests))
    
    def reset(self, identifier: Optional[str] = None) -> None:
        """Reset rate limit for identifier or all"""
        if identifier:
            self.requests.pop(identifier, None)
        else:
            self.requests.clear()

# ============================================================================
# CSRF Protection
# ============================================================================

class CSRFProtection:
    """CSRF protection for web extension requests"""
    
    def __init__(self):
        self.tokens: Dict[str, Tuple[str, float]] = {}
        self.token_lifetime = 3600  # 1 hour
    
    def generate_token(self, session_id: str) -> str:
        """Generate CSRF token for session"""
        token = secrets.token_urlsafe(32)
        self.tokens[session_id] = (token, time.time())
        return token
    
    def validate_token(self, session_id: str, token: str) -> bool:
        """Validate CSRF token"""
        if session_id not in self.tokens:
            return False
        
        stored_token, created_time = self.tokens[session_id]
        
        # Check if token expired
        if time.time() - created_time > self.token_lifetime:
            del self.tokens[session_id]
            return False
        
        # Constant-time comparison
        return hmac.compare_digest(stored_token, token)
    
    def cleanup_expired_tokens(self) -> None:
        """Remove expired tokens"""
        now = time.time()
        expired_sessions = [
            session_id for session_id, (_, created_time) in self.tokens.items()
            if now - created_time > self.token_lifetime
        ]
        
        for session_id in expired_sessions:
            del self.tokens[session_id]

# ============================================================================
# Session Security
# ============================================================================

class SessionSecurity:
    """Session security management"""
    
    def __init__(self, timeout_seconds: int = 1800):
        self.timeout_seconds = timeout_seconds
        self.sessions: Dict[str, Dict[str, Any]] = {}
    
    def create_session(self, session_id: str, data: Dict[str, Any]) -> None:
        """Create secure session"""
        self.sessions[session_id] = {
            **data,
            'created_at': time.time(),
            'last_activity': time.time(),
            'security_level': 'standard'
        }
    
    def update_activity(self, session_id: str) -> bool:
        """Update session activity"""
        if session_id in self.sessions:
            self.sessions[session_id]['last_activity'] = time.time()
            return True
        return False
    
    def is_session_valid(self, session_id: str) -> bool:
        """Check if session is valid and not expired"""
        if session_id not in self.sessions:
            return False
        
        session = self.sessions[session_id]
        last_activity = session['last_activity']
        
        return time.time() - last_activity < self.timeout_seconds
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data if valid"""
        if self.is_session_valid(session_id):
            return self.sessions[session_id].copy()
        return None
    
    def cleanup_expired_sessions(self) -> int:
        """Remove expired sessions"""
        now = time.time()
        expired_sessions = [
            session_id for session_id, session_data in self.sessions.items()
            if now - session_data['last_activity'] > self.timeout_seconds
        ]
        
        for session_id in expired_sessions:
            del self.sessions[session_id]
        
        return len(expired_sessions)
    
    def revoke_session(self, session_id: str) -> bool:
        """Revoke session"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            return True
        return False

# ============================================================================
# Security Decorators
# ============================================================================

def validate_input(validator_func):
    """Decorator for input validation"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract request data (assuming it's in kwargs)
            request_data = kwargs.get('data') or kwargs.get('request_data')
            
            if request_data:
                is_valid, errors = validator_func(request_data)
                if not is_valid:
                    logger.warning(f"Input validation failed: {errors}")
                    return {
                        'success': False,
                        'error': 'Input validation failed',
                        'details': errors
                    }
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def rate_limit(limiter: RateLimiter, identifier_func=None):
    """Decorator for rate limiting"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Get identifier (default to session_id)
            if identifier_func:
                identifier = identifier_func(*args, **kwargs)
            else:
                identifier = kwargs.get('session_id', 'default')
            
            if not limiter.is_allowed(identifier):
                logger.warning(f"Rate limit exceeded for {identifier}")
                return {
                    'success': False,
                    'error': 'Rate limit exceeded',
                    'retry_after': limiter.window_seconds
                }
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def require_valid_session(session_manager: SessionSecurity):
    """Decorator to require valid session"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            session_id = kwargs.get('session_id')
            
            if not session_id or not session_manager.is_session_valid(session_id):
                logger.warning(f"Invalid session: {session_id}")
                return {
                    'success': False,
                    'error': 'Invalid or expired session'
                }
            
            # Update session activity
            session_manager.update_activity(session_id)
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# ============================================================================
# Security Manager
# ============================================================================

class SecurityManager:
    """Central security manager for web extension server"""
    
    def __init__(self, config: SecurityConfig):
        self.config = config
        self.validator = InputValidator()
        self.rate_limiter = RateLimiter(
            max_requests=config.rate_limit_requests,
            window_seconds=config.rate_limit_window
        )
        self.csrf_protection = CSRFProtection()
        self.session_security = SessionSecurity(timeout_seconds=config.session_timeout)
        
        logger.info("Security manager initialized")
    
    def validate_origin(self, origin: str) -> bool:
        """Validate request origin"""
        if not origin:
            return False
        
        # Check allowed origins
        for allowed in self.config.allowed_origins:
            if origin.startswith(allowed):
                return True
        
        # Check trusted domains for web origins
        try:
            parsed = urlparse(origin)
            return parsed.hostname in self.config.trusted_domains
        except Exception:
            return False
    
    def validate_request_size(self, data: Any) -> bool:
        """Validate request size"""
        try:
            import json
            size = len(json.dumps(data))
            return size <= self.config.max_request_size
        except Exception:
            return False
    
    def create_secure_response(self, data: Any, session_id: Optional[str] = None) -> Dict[str, Any]:
        """Create secure response with CSRF token"""
        response = {
            'success': True,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }
        
        if session_id and self.config.enable_csrf_protection:
            response['csrf_token'] = self.csrf_protection.generate_token(session_id)
        
        return response
    
    def cleanup(self) -> None:
        """Cleanup expired tokens and sessions"""
        expired_sessions = self.session_security.cleanup_expired_sessions()
        self.csrf_protection.cleanup_expired_tokens()
        
        if expired_sessions > 0:
            logger.info(f"Cleaned up {expired_sessions} expired sessions")
    
    def get_security_stats(self) -> Dict[str, Any]:
        """Get security statistics"""
        return {
            'active_sessions': len(self.session_security.sessions),
            'active_csrf_tokens': len(self.csrf_protection.tokens),
            'rate_limit_enabled': self.config.enable_rate_limiting,
            'input_validation_enabled': self.config.enable_input_validation,
            'csrf_protection_enabled': self.config.enable_csrf_protection,
        }

# ============================================================================
# Global Security Instance
# ============================================================================

_security_manager: Optional[SecurityManager] = None

def get_security_manager() -> SecurityManager:
    """Get global security manager instance"""
    global _security_manager
    if _security_manager is None:
        config = SecurityConfig()
        _security_manager = SecurityManager(config)
    return _security_manager

def initialize_security(config: SecurityConfig) -> SecurityManager:
    """Initialize global security manager"""
    global _security_manager
    _security_manager = SecurityManager(config)
    return _security_manager

# ============================================================================
# Export Public API
# ============================================================================

__all__ = [
    'SecurityConfig',
    'InputValidator',
    'RateLimiter',
    'CSRFProtection',
    'SessionSecurity',
    'SecurityManager',
    'validate_input',
    'rate_limit',
    'require_valid_session',
    'get_security_manager',
    'initialize_security',
]