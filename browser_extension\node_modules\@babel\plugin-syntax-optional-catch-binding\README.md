# @babel/plugin-syntax-optional-catch-binding

> Allow parsing of optional catch bindings

See our website [@babel/plugin-syntax-optional-catch-binding](https://babeljs.io/docs/en/next/babel-plugin-syntax-optional-catch-binding.html) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-syntax-optional-catch-binding
```

or using yarn:

```sh
yarn add @babel/plugin-syntax-optional-catch-binding --dev
```
