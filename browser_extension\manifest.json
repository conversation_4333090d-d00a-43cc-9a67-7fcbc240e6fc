{"manifest_version": 3, "name": "ScreenMonitorMCP Web Extension", "version": "2.0.0", "description": "Modern browser extension for ScreenMonitorMCP - enables AI assistants to monitor and interact with web pages securely", "permissions": ["activeTab", "storage", "scripting", "tabs"], "host_permissions": ["http://localhost:7777/*", "https://localhost:7777/*"], "content_scripts": [{"matches": ["<all_urls>"], "js": ["dist/content-script.js"], "run_at": "document_start", "all_frames": false}], "background": {"service_worker": "dist/background.js", "type": "module"}, "action": {"default_popup": "popup.html", "default_title": "ScreenMonitorMCP Extension", "default_icon": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}}, "options_page": "options.html", "web_accessible_resources": [{"resources": ["dist/injected-script.js", "test-page.html"], "matches": ["<all_urls>"]}], "icons": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "externally_connectable": {"matches": ["http://localhost:*/*", "https://localhost:*/*", "file://*/*"]}}