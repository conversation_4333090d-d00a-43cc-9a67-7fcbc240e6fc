"""
ScreenMonitorMCP Web Extension Server
Modern server component for browser extension integration with MCP server
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict

from .models import (
    SessionInfo, DOMEvent, SmartClickRequest, SmartClickResult,
    APIResponse, RegistrationRequest, DOMEventRequest,
    SessionStatus, EventType, ResponseStatus
)
from .security import SecurityManager, SessionSecurity, RateLimiter
from .config import get_config
from . import get_config as get_extension_config

logger = logging.getLogger('web_extension.server')

@dataclass
class ExtensionSession:
    """Browser extension session data"""
    session_id: str
    domain: str
    features: List[str]
    user_agent: str
    registered_at: datetime
    last_activity: datetime
    status: SessionStatus = SessionStatus.ACTIVE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "session_id": self.session_id,
            "domain": self.domain,
            "features": self.features,
            "user_agent": self.user_agent,
            "registered_at": self.registered_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "status": self.status.value
        }
    
    def is_expired(self, timeout_seconds: int = 1800) -> bool:
        """Check if session is expired"""
        return datetime.now() - self.last_activity > timedelta(seconds=timeout_seconds)
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.now()

class ExtensionServer:
    """Web Extension Server for MCP integration"""
    
    def __init__(self, mcp_server=None):
        self.mcp_server = mcp_server
        self.config = get_config()
        self.extension_config = get_extension_config()
        
        # Session management
        self.active_sessions: Dict[str, ExtensionSession] = {}
        self.session_security = SessionSecurity(timeout_seconds=self.config.security.session_timeout)
        self.rate_limiter = RateLimiter(
            max_requests=self.config.security.rate_limit_requests,
            window_seconds=self.config.security.rate_limit_window
        )
        
        # Security manager
        self.security_manager = SecurityManager(self.config.security)
        
        # Event storage
        self.dom_events: List[DOMEvent] = []
        self.smart_click_history: List[SmartClickResult] = []
        
        # Allowed domains from config
        self.allowed_domains = self.extension_config.allowed_domains
        
        logger.info("Extension server initialized")
    
    def _is_domain_allowed(self, domain: str) -> bool:
        """Check if domain is in whitelist"""
        if not domain:
            return False
        
        # Check exact match and subdomain patterns
        for allowed in self.allowed_domains:
            if domain == allowed or domain.endswith(f".{allowed}"):
                return True
        return False
    
    def _generate_session_id(self) -> str:
        """Generate unique session ID"""
        return f"ext_{uuid.uuid4().hex[:16]}"
    
    def _validate_session(self, session_id: str) -> Optional[ExtensionSession]:
        """Validate and return session if valid"""
        if not session_id or session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        
        # Check if expired
        if session.is_expired(self.config.security.session_timeout):
            self._cleanup_session(session_id)
            return None
        
        # Update activity
        session.update_activity()
        return session
    
    def _cleanup_session(self, session_id: str):
        """Remove expired session"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
            logger.info("Session cleaned up")
    
    def _cleanup_expired_sessions(self):
        """Clean up all expired sessions"""
        expired_sessions = [
            sid for sid, session in self.active_sessions.items()
            if session.is_expired(self.config.security.session_timeout)
        ]
        
        for session_id in expired_sessions:
            self._cleanup_session(session_id)
        
        if expired_sessions:
            logger.info("Cleaned up expired sessions")
    
    async def register_extension(self, request: RegistrationRequest) -> APIResponse:
        """Register browser extension and create session"""
        try:
            # Validate domain
            if not self._is_domain_allowed(request.domain):
                return APIResponse(
                    success=False,
                    error=f"Domain '{request.domain}' is not in whitelist",
                    data={"allowed_domains": self.allowed_domains}
                )
            
            # Generate session ID if not provided
            session_id = request.session_id or self._generate_session_id()
            
            # Create session
            session = ExtensionSession(
                session_id=session_id,
                domain=request.domain,
                features=request.features,
                user_agent=request.user_agent,
                registered_at=datetime.now(),
                last_activity=datetime.now()
            )
            
            # Store session
            self.active_sessions[session_id] = session
            
            logger.info("Extension registered")
            
            return APIResponse(
                success=True,
                data={
                    "session_id": session_id,
                    "enabled_features": request.features,
                    "server_version": "2.1.0-mcp-integrated",
                    "allowed_domains": self.allowed_domains
                }
            )
            
        except Exception as e:
            logger.error("Extension registration failed")
            return APIResponse(success=False, error=str(e))
    
    async def process_dom_event(self, request: DOMEventRequest) -> APIResponse:
        """Process DOM event from browser extension"""
        try:
            # Validate session
            session = self._validate_session(request.session_id)
            if not session:
                return APIResponse(success=False, error="Invalid or expired session")
            
            # Create DOM event
            dom_event = DOMEvent(
                event_id=f"evt_{uuid.uuid4().hex[:12]}",
                session_id=request.session_id,
                event_type=request.event_type,
                event_data=request.event_data,
                timestamp=datetime.now(),
                processed=False
            )
            
            # Store event
            self.dom_events.append(dom_event)
            
            # Process event if MCP server is available
            analysis_result = None
            if self.mcp_server:
                analysis_result = await self._analyze_dom_event(dom_event)
            
            logger.info("DOM event processed")
            
            return APIResponse(
                success=True,
                data={
                    "event_id": dom_event.event_id,
                    "processed": True,
                    "analysis": analysis_result,
                    "timestamp": dom_event.timestamp.isoformat()
                }
            )
            
        except Exception as e:
            logger.error("DOM event processing failed")
            return APIResponse(success=False, error=str(e))
    
    async def execute_smart_click(self, request: SmartClickRequest) -> APIResponse:
        """Execute smart click through MCP server"""
        try:
            # Validate session
            session = self._validate_session(request.session_id)
            if not session:
                return APIResponse(success=False, error="Invalid or expired session")
            
            # Execute smart click via MCP server
            if not self.mcp_server:
                return APIResponse(success=False, error="MCP server not available")
            
            # Call MCP smart_click tool
            result = await self._execute_mcp_smart_click(request)
            
            # Store result
            smart_click_result = SmartClickResult(
                request_id=f"sc_{uuid.uuid4().hex[:12]}",
                session_id=request.session_id,
                element_description=request.element_description,
                success=result.get("success", False),
                coordinates=result.get("coordinates"),
                confidence=result.get("confidence", 0.0),
                error_message=result.get("error"),
                timestamp=datetime.now()
            )
            
            self.smart_click_history.append(smart_click_result)
            
            logger.info("Smart click executed")
            
            return APIResponse(
                success=smart_click_result.success,
                data=smart_click_result.to_dict(),
                error=smart_click_result.error_message
            )
            
        except Exception as e:
            logger.error("Smart click execution failed")
            return APIResponse(success=False, error=str(e))
    
    async def _analyze_dom_event(self, event: DOMEvent) -> Optional[Dict[str, Any]]:
        """Analyze DOM event using MCP server capabilities"""
        try:
            # This could integrate with MCP server's analysis capabilities
            # For now, return basic analysis
            return {
                "event_type": event.event_type.value,
                "analysis": "DOM event received and processed",
                "recommendations": []
            }
        except Exception as e:
            logger.error("DOM event analysis failed")
            return None
    
    async def _execute_mcp_smart_click(self, request: SmartClickRequest) -> Dict[str, Any]:
        """Execute smart click via MCP server"""
        try:
            if not self.mcp_server:
                return {"success": False, "error": "MCP server not available"}

            # Get the smart click tool from MCP server
            # This integrates with the actual smart_click functionality
            from ui_detection import get_smart_clicker

            smart_clicker = get_smart_clicker()
            if not smart_clicker:
                return {"success": False, "error": "Smart clicker not available"}

            # Execute smart click with the element description
            result = await smart_clicker.find_and_click(
                element_description=request.element_description,
                confidence_threshold=request.confidence_threshold,
                dry_run=False  # Actually perform the click
            )

            if result.get("success"):
                return {
                    "success": True,
                    "coordinates": result.get("coordinates", {"x": 0, "y": 0}),
                    "confidence": result.get("confidence", 0.0),
                    "element_found": result.get("element_found", False),
                    "click_performed": result.get("click_performed", False)
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Smart click failed"),
                    "element_found": result.get("element_found", False)
                }

        except Exception as e:
            logger.error("MCP smart click failed")
            return {"success": False, "error": str(e)}
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information"""
        session = self._validate_session(session_id)
        if not session:
            return None
        
        return session.to_dict()
    
    def get_all_sessions(self) -> List[Dict[str, Any]]:
        """Get all active sessions"""
        self._cleanup_expired_sessions()
        return [session.to_dict() for session in self.active_sessions.values()]
    
    def get_server_stats(self) -> Dict[str, Any]:
        """Get server statistics"""
        self._cleanup_expired_sessions()
        
        return {
            "active_sessions": len(self.active_sessions),
            "total_dom_events": len(self.dom_events),
            "total_smart_clicks": len(self.smart_click_history),
            "allowed_domains": self.allowed_domains,
            "server_version": "2.1.0-mcp-integrated",
            "uptime": datetime.now().isoformat()
        }

# Global extension server instance
_extension_server: Optional[ExtensionServer] = None

def initialize_extension_server(mcp_server=None) -> ExtensionServer:
    """Initialize global extension server"""
    global _extension_server
    _extension_server = ExtensionServer(mcp_server)
    return _extension_server

def get_extension_server() -> Optional[ExtensionServer]:
    """Get global extension server instance"""
    return _extension_server
