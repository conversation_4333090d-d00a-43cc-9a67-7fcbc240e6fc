"""
Web Extension Configuration Management

Centralized configuration management for the Python web extension server
with environment-based settings, validation, and runtime updates.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field, asdict
from pathlib import Path
from enum import Enum

logger = logging.getLogger(__name__)

# ============================================================================
# Configuration Enums
# ============================================================================

class Environment(Enum):
    """Environment types"""
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    TEST = "test"

class LogLevel(Enum):
    """Log levels"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class SecurityLevel(Enum):
    """Security levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

# ============================================================================
# Configuration Data Classes
# ============================================================================

@dataclass
class ServerConfig:
    """Server configuration"""
    host: str = "localhost"
    port: int = 7777
    debug: bool = False
    reload: bool = False
    workers: int = 1
    max_connections: int = 100
    timeout: int = 30
    
@dataclass
class SecurityConfig:
    """Security configuration"""
    enable_rate_limiting: bool = True
    enable_input_validation: bool = True
    enable_csrf_protection: bool = True
    enable_cors: bool = True
    max_request_size: int = 1024 * 1024  # 1MB
    rate_limit_requests: int = 100
    rate_limit_window: int = 60
    session_timeout: int = 1800  # 30 minutes
    allowed_origins: List[str] = field(default_factory=lambda: [
        "chrome-extension://",
        "moz-extension://",
        "http://localhost",
        "https://localhost"
    ])
    trusted_domains: List[str] = field(default_factory=lambda: [
        "localhost",
        "127.0.0.1",
        "file"
    ])

@dataclass
class ExtensionConfig:
    """Extension-specific configuration"""
    enabled: bool = True
    features: Dict[str, bool] = field(default_factory=lambda: {
        "web_monitoring": True,
        "dom_detection": True,
        "web_smart_click": True,
        "domain_whitelist": True,
        "web_automation": True,
        "event_analysis": True
    })
    allowed_domains: List[str] = field(default_factory=lambda: [
        "localhost",
        "127.0.0.1",
        "file",
        "github.com",
        "stackoverflow.com"
    ])
    auto_sync_domains: bool = True
    session_cleanup_interval: int = 300  # 5 minutes

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: LogLevel = LogLevel.INFO
    format: str = "[%(name)s] %(levelname)s - %(message)s"
    file_enabled: bool = False
    file_path: Optional[str] = None
    file_max_size: int = 10 * 1024 * 1024  # 10MB
    file_backup_count: int = 5
    console_enabled: bool = True

@dataclass
class DatabaseConfig:
    """Database configuration (for future use)"""
    enabled: bool = False
    url: str = "sqlite:///web_extension.db"
    pool_size: int = 5
    max_overflow: int = 10
    echo: bool = False

@dataclass
class WebExtensionConfig:
    """Complete web extension configuration"""
    environment: Environment = Environment.PRODUCTION
    version: str = "2.0.0"
    server: ServerConfig = field(default_factory=ServerConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    extension: ExtensionConfig = field(default_factory=ExtensionConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), indent=2, default=str)

# ============================================================================
# Environment Configurations
# ============================================================================

def get_development_config() -> WebExtensionConfig:
    """Get development environment configuration"""
    return WebExtensionConfig(
        environment=Environment.DEVELOPMENT,
        server=ServerConfig(
            debug=True,
            reload=True,
            workers=1
        ),
        security=SecurityConfig(
            enable_rate_limiting=False,
            enable_csrf_protection=False,
            allowed_origins=[
                "chrome-extension://",
                "moz-extension://",
                "http://localhost",
                "https://localhost",
                "http://127.0.0.1",
                "https://127.0.0.1"
            ],
            trusted_domains=[
                "localhost",
                "127.0.0.1",
                "file",
                "github.com",
                "stackoverflow.com"
            ]
        ),
        extension=ExtensionConfig(
            allowed_domains=[
                "localhost",
                "127.0.0.1",
                "file",
                "github.com",
                "stackoverflow.com",
                "developer.mozilla.org"
            ]
        ),
        logging=LoggingConfig(
            level=LogLevel.DEBUG,
            console_enabled=True,
            file_enabled=True,
            file_path="logs/web_extension_dev.log"
        )
    )

def get_production_config() -> WebExtensionConfig:
    """Get production environment configuration"""
    return WebExtensionConfig(
        environment=Environment.PRODUCTION,
        server=ServerConfig(
            debug=False,
            reload=False,
            workers=4,
            max_connections=1000
        ),
        security=SecurityConfig(
            enable_rate_limiting=True,
            enable_input_validation=True,
            enable_csrf_protection=True,
            rate_limit_requests=50,
            allowed_origins=[
                "chrome-extension://",
                "moz-extension://",
                "http://localhost",
                "https://localhost"
            ],
            trusted_domains=[
                "localhost",
                "127.0.0.1",
                "file"
            ]
        ),
        extension=ExtensionConfig(
            features={
                "web_monitoring": True,
                "dom_detection": True,
                "web_smart_click": True,
                "domain_whitelist": True,
                "web_automation": False,  # Disabled in production
                "event_analysis": True
            },
            allowed_domains=[
                "localhost",
                "127.0.0.1",
                "file"
            ]
        ),
        logging=LoggingConfig(
            level=LogLevel.WARNING,
            console_enabled=True,
            file_enabled=True,
            file_path="logs/web_extension.log"
        )
    )

def get_test_config() -> WebExtensionConfig:
    """Get test environment configuration"""
    return WebExtensionConfig(
        environment=Environment.TEST,
        server=ServerConfig(
            port=7778,
            debug=True,
            workers=1
        ),
        security=SecurityConfig(
            enable_rate_limiting=False,
            enable_csrf_protection=False,
            session_timeout=300  # 5 minutes for tests
        ),
        extension=ExtensionConfig(
            features={
                "web_monitoring": True,
                "dom_detection": True,
                "web_smart_click": True,
                "domain_whitelist": True,
                "web_automation": False,
                "event_analysis": False
            },
            allowed_domains=[
                "localhost",
                "127.0.0.1",
                "file"
            ]
        ),
        logging=LoggingConfig(
            level=LogLevel.DEBUG,
            console_enabled=True,
            file_enabled=False
        )
    )

# ============================================================================
# Configuration Manager
# ============================================================================

class ConfigurationManager:
    """Configuration manager for web extension server"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or os.getenv("WEB_EXTENSION_CONFIG_FILE")
        self._config: Optional[WebExtensionConfig] = None
        self._environment = self._detect_environment()
        
    def _detect_environment(self) -> Environment:
        """Detect current environment"""
        env_name = os.getenv("WEB_EXTENSION_ENV", "production").lower()
        
        try:
            return Environment(env_name)
        except ValueError:
            logger.warning(f"Unknown environment '{env_name}', defaulting to production")
            return Environment.PRODUCTION
    
    def load_config(self) -> WebExtensionConfig:
        """Load configuration from environment and files"""
        if self._config is not None:
            return self._config
        
        # Start with environment-based config
        if self._environment == Environment.DEVELOPMENT:
            config = get_development_config()
        elif self._environment == Environment.TEST:
            config = get_test_config()
        else:
            config = get_production_config()
        
        # Override with environment variables
        config = self._apply_env_overrides(config)
        
        # Override with config file if exists
        if self.config_file and os.path.exists(self.config_file):
            config = self._apply_file_overrides(config)
        
        # Validate configuration
        self._validate_config(config)
        
        self._config = config
        logger.info(f"Configuration loaded for {config.environment.value} environment")
        
        return config
    
    def _apply_env_overrides(self, config: WebExtensionConfig) -> WebExtensionConfig:
        """Apply environment variable overrides"""
        
        # Server configuration
        if os.getenv("WEB_EXTENSION_HOST"):
            config.server.host = os.getenv("WEB_EXTENSION_HOST")
        
        if os.getenv("WEB_EXTENSION_PORT"):
            try:
                config.server.port = int(os.getenv("WEB_EXTENSION_PORT"))
            except ValueError:
                logger.warning("Invalid WEB_EXTENSION_PORT value")
        
        if os.getenv("WEB_EXTENSION_DEBUG"):
            config.server.debug = os.getenv("WEB_EXTENSION_DEBUG").lower() == "true"
        
        # Extension configuration
        if os.getenv("ENABLE_WEB_EXTENSION"):
            config.extension.enabled = os.getenv("ENABLE_WEB_EXTENSION").lower() == "true"
        
        if os.getenv("ALLOWED_DOMAINS"):
            domains = [d.strip() for d in os.getenv("ALLOWED_DOMAINS").split(",")]
            config.extension.allowed_domains = domains
        
        # Security configuration
        if os.getenv("ENABLE_RATE_LIMITING"):
            config.security.enable_rate_limiting = os.getenv("ENABLE_RATE_LIMITING").lower() == "true"
        
        if os.getenv("RATE_LIMIT_REQUESTS"):
            try:
                config.security.rate_limit_requests = int(os.getenv("RATE_LIMIT_REQUESTS"))
            except ValueError:
                logger.warning("Invalid RATE_LIMIT_REQUESTS value")
        
        # Logging configuration
        if os.getenv("LOG_LEVEL"):
            try:
                config.logging.level = LogLevel(os.getenv("LOG_LEVEL").upper())
            except ValueError:
                logger.warning("Invalid LOG_LEVEL value")
        
        if os.getenv("LOG_FILE"):
            config.logging.file_enabled = True
            config.logging.file_path = os.getenv("LOG_FILE")
        
        return config
    
    def _apply_file_overrides(self, config: WebExtensionConfig) -> WebExtensionConfig:
        """Apply configuration file overrides"""
        try:
            with open(self.config_file, 'r') as f:
                file_config = json.load(f)
            
            # Deep merge configuration
            config_dict = config.to_dict()
            self._deep_merge(config_dict, file_config)
            
            # Convert back to dataclass (simplified approach)
            # In a real implementation, you'd want proper deserialization
            return config
            
        except Exception as e:
            logger.error(f"Failed to load config file {self.config_file}: {e}")
            return config
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> None:
        """Deep merge two dictionaries"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value
    
    def _validate_config(self, config: WebExtensionConfig) -> None:
        """Validate configuration"""
        errors = []
        
        # Validate server config
        if not (1 <= config.server.port <= 65535):
            errors.append(f"Invalid port number: {config.server.port}")
        
        if config.server.workers < 1:
            errors.append(f"Workers must be >= 1: {config.server.workers}")
        
        # Validate security config
        if config.security.rate_limit_requests < 1:
            errors.append(f"Rate limit requests must be >= 1: {config.security.rate_limit_requests}")
        
        if config.security.session_timeout < 60:
            errors.append(f"Session timeout must be >= 60 seconds: {config.security.session_timeout}")
        
        # Validate extension config
        if not config.extension.allowed_domains:
            errors.append("At least one allowed domain must be specified")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
    
    def get_config(self) -> WebExtensionConfig:
        """Get current configuration"""
        if self._config is None:
            return self.load_config()
        return self._config
    
    def reload_config(self) -> WebExtensionConfig:
        """Reload configuration"""
        self._config = None
        return self.load_config()
    
    def save_config(self, config: WebExtensionConfig, file_path: Optional[str] = None) -> None:
        """Save configuration to file"""
        target_file = file_path or self.config_file
        if not target_file:
            raise ValueError("No config file specified")
        
        # Ensure directory exists
        Path(target_file).parent.mkdir(parents=True, exist_ok=True)
        
        with open(target_file, 'w') as f:
            f.write(config.to_json())
        
        logger.info(f"Configuration saved to {target_file}")

# ============================================================================
# Global Configuration Instance
# ============================================================================

_config_manager: Optional[ConfigurationManager] = None

def get_config_manager() -> ConfigurationManager:
    """Get global configuration manager"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigurationManager()
    return _config_manager

def get_config() -> WebExtensionConfig:
    """Get current configuration"""
    return get_config_manager().get_config()

def reload_config() -> WebExtensionConfig:
    """Reload configuration"""
    return get_config_manager().reload_config()

# ============================================================================
# Configuration Utilities
# ============================================================================

def is_feature_enabled(feature: str) -> bool:
    """Check if a feature is enabled"""
    config = get_config()
    return config.extension.enabled and config.extension.features.get(feature, False)

def is_domain_allowed(domain: str) -> bool:
    """Check if domain is allowed"""
    if not domain:
        return False
    
    config = get_config()
    if not config.extension.enabled:
        return False
    
    allowed = config.extension.allowed_domains
    
    # Direct match
    if domain in allowed:
        return True
    
    # Subdomain check
    for allowed_domain in allowed:
        if domain.endswith('.' + allowed_domain):
            return True
    
    return False

def get_server_config() -> ServerConfig:
    """Get server configuration"""
    return get_config().server

def get_security_config() -> SecurityConfig:
    """Get security configuration"""
    return get_config().security

def get_extension_config() -> ExtensionConfig:
    """Get extension configuration"""
    return get_config().extension

def get_logging_config() -> LoggingConfig:
    """Get logging configuration"""
    return get_config().logging

# ============================================================================
# Configuration Validation
# ============================================================================

def validate_domain(domain: str) -> bool:
    """Validate domain format"""
    if not domain or not isinstance(domain, str):
        return False
    
    # Basic domain validation
    domain = domain.strip().lower()
    
    # Special cases
    if domain in ['localhost', '127.0.0.1', 'file']:
        return True
    
    # Basic format check
    import re
    domain_pattern = re.compile(
        r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?'
        r'(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    )
    
    return bool(domain_pattern.match(domain)) and len(domain) <= 253

def validate_feature(feature: str) -> bool:
    """Validate feature name"""
    valid_features = {
        "web_monitoring",
        "dom_detection", 
        "web_smart_click",
        "domain_whitelist",
        "web_automation",
        "event_analysis"
    }
    return feature in valid_features

# ============================================================================
# Export Public API
# ============================================================================

__all__ = [
    # Enums
    'Environment',
    'LogLevel',
    'SecurityLevel',
    
    # Data classes
    'ServerConfig',
    'SecurityConfig',
    'ExtensionConfig',
    'LoggingConfig',
    'DatabaseConfig',
    'WebExtensionConfig',
    
    # Manager
    'ConfigurationManager',
    
    # Global functions
    'get_config_manager',
    'get_config',
    'reload_config',
    
    # Utility functions
    'is_feature_enabled',
    'is_domain_allowed',
    'get_server_config',
    'get_security_config',
    'get_extension_config',
    'get_logging_config',
    
    # Validation
    'validate_domain',
    'validate_feature',
    
    # Environment configs
    'get_development_config',
    'get_production_config',
    'get_test_config',
]