<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScreenMonitorMCP Web Extension Test Page</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .test-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-card h3 {
            color: #2980b9;
            margin-bottom: 15px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .btn-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .log-area {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .interactive-elements {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }

        .interactive-elements > * {
            flex: 1;
            min-width: 200px;
        }

        #smart-click-target {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px dashed #e74c3c;
        }

        #smart-click-target:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .extension-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 1000;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="extension-status" id="extensionStatus">
        <div id="extensionIndicator">🔍 Checking Extension...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🚀 ScreenMonitorMCP Test Page</h1>
            <p>Comprehensive testing environment for Web Extension and Browser Extension features</p>
        </div>

        <div class="content">
            <!-- Extension Status Section -->
            <div class="section">
                <h2>📊 Extension Status & Connection</h2>
                <div class="test-grid">
                    <div class="test-card">
                        <h3>🔌 MCP Server Connection</h3>
                        <button class="btn btn-info" onclick="testMCPConnection()">Test MCP Server</button>
                        <div id="mcpStatus" class="status info">Ready to test...</div>
                    </div>
                    <div class="test-card">
                        <h3>🧩 Browser Extension</h3>
                        <button class="btn btn-info" onclick="testExtensionConnection()">Test Extension</button>
                        <div id="extensionConnectionStatus" class="status info">Ready to test...</div>
                    </div>
                </div>
            </div>

            <!-- Smart Click Testing -->
            <div class="section">
                <h2>🎯 Smart Click Testing</h2>
                <div class="test-grid">
                    <div class="test-card">
                        <h3>🎪 Smart Click Target</h3>
                        <div id="smart-click-target" class="pulse">
                            <h4>🎯 CLICK ME!</h4>
                            <p>This is the smart click target</p>
                            <p>ID: smart-click-target</p>
                        </div>
                        <button class="btn btn-success" onclick="testSmartClick()">Test Smart Click via Extension</button>
                    </div>
                    <div class="test-card">
                        <h3>⚙️ Smart Click Configuration</h3>
                        <div class="form-group">
                            <label>Element Description:</label>
                            <input type="text" id="smartClickDescription" value="Click the smart click target button">
                        </div>
                        <div class="form-group">
                            <label>Confidence Threshold:</label>
                            <input type="range" id="confidenceThreshold" min="0.1" max="1.0" step="0.1" value="0.8">
                            <span id="confidenceValue">0.8</span>
                        </div>
                        <button class="btn btn-warning" onclick="executeSmartClick()">Execute Smart Click</button>
                    </div>
                </div>
            </div>

            <!-- Interactive Elements for Testing -->
            <div class="section">
                <h2>🎮 Interactive Test Elements</h2>
                <div class="interactive-elements">
                    <button class="btn btn-success" id="test-button-1" onclick="logEvent('Button 1 clicked')">Test Button 1</button>
                    <button class="btn btn-warning" id="test-button-2" onclick="logEvent('Button 2 clicked')">Test Button 2</button>
                    <button class="btn btn-info" id="submit-button" onclick="logEvent('Submit button clicked')">Submit Button</button>
                </div>
                
                <div class="form-group">
                    <label>Test Input Field:</label>
                    <input type="text" id="test-input" placeholder="Type something here..." onchange="logEvent('Input changed: ' + this.value)">
                </div>
                
                <div class="form-group">
                    <label>Test Select:</label>
                    <select id="test-select" onchange="logEvent('Select changed: ' + this.value)">
                        <option value="">Choose an option...</option>
                        <option value="option1">Option 1</option>
                        <option value="option2">Option 2</option>
                        <option value="option3">Option 3</option>
                    </select>
                </div>
            </div>

            <!-- DOM Event Testing -->
            <div class="section">
                <h2>📡 DOM Event Testing</h2>
                <div class="test-grid">
                    <div class="test-card">
                        <h3>🎪 Event Simulation</h3>
                        <button class="btn btn-info" onclick="simulateClick()">Simulate Click Event</button>
                        <button class="btn btn-info" onclick="simulateFormSubmit()">Simulate Form Submit</button>
                        <button class="btn btn-info" onclick="simulateKeyPress()">Simulate Key Press</button>
                    </div>
                    <div class="test-card">
                        <h3>📊 Event Monitoring</h3>
                        <button class="btn btn-success" onclick="startEventMonitoring()">Start Monitoring</button>
                        <button class="btn btn-warning" onclick="stopEventMonitoring()">Stop Monitoring</button>
                        <div id="eventMonitoringStatus" class="status info">Monitoring stopped</div>
                    </div>
                </div>
            </div>

            <!-- Session Management -->
            <div class="section">
                <h2>🔐 Session Management</h2>
                <div class="test-grid">
                    <div class="test-card">
                        <h3>📝 Session Registration</h3>
                        <div class="form-group">
                            <label>Domain:</label>
                            <input type="text" id="sessionDomain" value="localhost">
                        </div>
                        <div class="form-group">
                            <label>Features:</label>
                            <select id="sessionFeatures" multiple>
                                <option value="dom_monitoring" selected>DOM Monitoring</option>
                                <option value="smart_click" selected>Smart Click</option>
                                <option value="text_extraction">Text Extraction</option>
                                <option value="event_analysis">Event Analysis</option>
                            </select>
                        </div>
                        <button class="btn btn-success" onclick="registerSession()">Register Session</button>
                        <div id="sessionStatus" class="status info">No session registered</div>
                    </div>
                    <div class="test-card">
                        <h3>📊 Session Info</h3>
                        <button class="btn btn-info" onclick="getSessionInfo()">Get Session Info</button>
                        <button class="btn btn-info" onclick="getAllSessions()">Get All Sessions</button>
                        <div id="sessionInfo" class="status info">Click to get session info</div>
                    </div>
                </div>
            </div>

            <!-- Activity Log -->
            <div class="section">
                <h2>📋 Activity Log</h2>
                <div class="test-card">
                    <button class="btn btn-warning" onclick="clearLog()">Clear Log</button>
                    <button class="btn btn-info" onclick="exportLog()">Export Log</button>
                    <div id="activityLog" class="log-area">
                        <div>🚀 ScreenMonitorMCP Test Page Loaded</div>
                        <div>📊 Ready for testing...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentSessionId = null;
        let eventMonitoring = false;
        let extensionAvailable = false;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            logEvent('🚀 Page loaded and ready for testing');
            checkExtensionAvailability();
            setupEventListeners();
            updateConfidenceDisplay();
        });

        // Check if extension is available
        function checkExtensionAvailability() {
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                extensionAvailable = true;
                document.getElementById('extensionIndicator').innerHTML = '✅ Extension Available';
                document.getElementById('extensionIndicator').style.color = 'green';
                logEvent('✅ Chrome extension detected');
            } else {
                extensionAvailable = false;
                document.getElementById('extensionIndicator').innerHTML = '❌ Extension Not Found';
                document.getElementById('extensionIndicator').style.color = 'red';
                logEvent('❌ Chrome extension not detected');
            }
        }

        // Setup event listeners
        function setupEventListeners() {
            // Confidence threshold slider
            document.getElementById('confidenceThreshold').addEventListener('input', updateConfidenceDisplay);
            
            // Add click listeners to all buttons for monitoring
            document.querySelectorAll('button').forEach(button => {
                button.addEventListener('click', function(e) {
                    if (eventMonitoring) {
                        logEvent(`🖱️ Button clicked: ${this.textContent} (ID: ${this.id || 'no-id'})`);
                    }
                });
            });
        }

        // Update confidence display
        function updateConfidenceDisplay() {
            const slider = document.getElementById('confidenceThreshold');
            const display = document.getElementById('confidenceValue');
            display.textContent = slider.value;
        }

        // Test MCP Server connection
        async function testMCPConnection() {
            const statusDiv = document.getElementById('mcpStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 Testing MCP server connection...';
            
            try {
                const response = await fetch('http://localhost:7777/api/health');
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.className = 'status success';
                    statusDiv.textContent = `✅ MCP Server connected! Version: ${data.version || 'Unknown'}`;
                    logEvent('✅ MCP Server connection successful');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ MCP Server connection failed: ${error.message}`;
                logEvent(`❌ MCP Server connection failed: ${error.message}`);
            }
        }

        // Test extension connection
        function testExtensionConnection() {
            const statusDiv = document.getElementById('extensionConnectionStatus');
            
            if (!extensionAvailable) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Extension not available';
                return;
            }

            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 Testing extension connection...';

            chrome.runtime.sendMessage({
                type: 'TEST_CONNECTION',
                timestamp: new Date().toISOString()
            }, function(response) {
                if (chrome.runtime.lastError) {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ Extension error: ${chrome.runtime.lastError.message}`;
                    logEvent(`❌ Extension connection failed: ${chrome.runtime.lastError.message}`);
                } else if (response && response.success) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ Extension connected successfully!';
                    logEvent('✅ Extension connection successful');
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ Extension test failed: ${response?.error || 'Unknown error'}`;
                    logEvent(`❌ Extension test failed: ${response?.error || 'Unknown error'}`);
                }
            });
        }

        // Test smart click via extension
        function testSmartClick() {
            if (!extensionAvailable) {
                logEvent('❌ Extension not available for smart click test');
                return;
            }

            logEvent('🎯 Testing smart click via extension...');

            chrome.runtime.sendMessage({
                type: 'SMART_CLICK',
                data: {
                    element_description: 'Click the smart click target',
                    confidence_threshold: 0.8
                }
            }, function(response) {
                if (response && response.success) {
                    logEvent('✅ Smart click test successful via extension');
                } else {
                    logEvent(`❌ Smart click test failed: ${response?.error || 'Unknown error'}`);
                }
            });
        }

        // Execute smart click
        async function executeSmartClick() {
            const description = document.getElementById('smartClickDescription').value;
            const confidence = document.getElementById('confidenceThreshold').value;

            logEvent(`🎯 Executing smart click: "${description}" (confidence: ${confidence})`);

            try {
                const response = await fetch('http://localhost:7777/api/extension/smart-click', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        session_id: currentSessionId,
                        element_description: description,
                        confidence_threshold: parseFloat(confidence)
                    })
                });

                const result = await response.json();
                if (result.success) {
                    logEvent('✅ Smart click executed successfully');
                } else {
                    logEvent(`❌ Smart click failed: ${result.error}`);
                }
            } catch (error) {
                logEvent(`❌ Smart click error: ${error.message}`);
            }
        }

        // Simulate events
        function simulateClick() {
            const event = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                clientX: 100,
                clientY: 200
            });

            document.getElementById('test-button-1').dispatchEvent(event);
            logEvent('🖱️ Simulated click event on test-button-1');
        }

        function simulateFormSubmit() {
            const event = new Event('submit', {
                bubbles: true,
                cancelable: true
            });

            logEvent('📝 Simulated form submit event');
        }

        function simulateKeyPress() {
            const event = new KeyboardEvent('keydown', {
                key: 'Enter',
                code: 'Enter',
                bubbles: true
            });

            document.getElementById('test-input').dispatchEvent(event);
            logEvent('⌨️ Simulated Enter key press on test-input');
        }

        // Event monitoring
        function startEventMonitoring() {
            eventMonitoring = true;
            document.getElementById('eventMonitoringStatus').className = 'status success';
            document.getElementById('eventMonitoringStatus').textContent = '✅ Event monitoring active';
            logEvent('📡 Event monitoring started');
        }

        function stopEventMonitoring() {
            eventMonitoring = false;
            document.getElementById('eventMonitoringStatus').className = 'status info';
            document.getElementById('eventMonitoringStatus').textContent = '⏹️ Event monitoring stopped';
            logEvent('⏹️ Event monitoring stopped');
        }

        // Session management
        async function registerSession() {
            const domain = document.getElementById('sessionDomain').value;
            const featuresSelect = document.getElementById('sessionFeatures');
            const features = Array.from(featuresSelect.selectedOptions).map(option => option.value);

            const statusDiv = document.getElementById('sessionStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 Registering session...';

            try {
                const response = await fetch('http://localhost:7777/api/extension/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        domain: domain,
                        features: features,
                        user_agent: navigator.userAgent
                    })
                });

                const result = await response.json();
                if (result.success) {
                    currentSessionId = result.data.session_id;
                    statusDiv.className = 'status success';
                    statusDiv.textContent = `✅ Session registered: ${currentSessionId}`;
                    logEvent(`✅ Session registered successfully: ${currentSessionId}`);
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ Registration failed: ${result.error}`;
                    logEvent(`❌ Session registration failed: ${result.error}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Registration error: ${error.message}`;
                logEvent(`❌ Session registration error: ${error.message}`);
            }
        }

        async function getSessionInfo() {
            if (!currentSessionId) {
                logEvent('❌ No session registered. Please register a session first.');
                return;
            }

            const statusDiv = document.getElementById('sessionInfo');
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 Getting session info...';

            try {
                // This would be an MCP call in real usage
                statusDiv.className = 'status success';
                statusDiv.textContent = `📊 Session: ${currentSessionId} (Active)`;
                logEvent(`📊 Session info retrieved for: ${currentSessionId}`);
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Error: ${error.message}`;
                logEvent(`❌ Session info error: ${error.message}`);
            }
        }

        async function getAllSessions() {
            const statusDiv = document.getElementById('sessionInfo');
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 Getting all sessions...';

            try {
                // This would be an MCP call in real usage
                statusDiv.className = 'status success';
                statusDiv.textContent = `📊 Total sessions: 1 (Current: ${currentSessionId || 'None'})`;
                logEvent('📊 All sessions retrieved');
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Error: ${error.message}`;
                logEvent(`❌ Get all sessions error: ${error.message}`);
            }
        }

        // Utility functions
        function logEvent(message) {
            const logArea = document.getElementById('activityLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('activityLog').innerHTML = '';
            logEvent('🧹 Log cleared');
        }

        function exportLog() {
            const logContent = document.getElementById('activityLog').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `screenmonitor-test-log-${new Date().toISOString().slice(0,19)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            logEvent('📁 Log exported to file');
        }

        // Smart click target interaction
        document.addEventListener('DOMContentLoaded', function() {
            const target = document.getElementById('smart-click-target');
            target.addEventListener('click', function() {
                this.style.background = 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)';
                this.innerHTML = '<h4>✅ SUCCESS!</h4><p>Smart click target was clicked!</p><p>ID: smart-click-target</p>';
                logEvent('🎯 Smart click target was successfully clicked!');

                setTimeout(() => {
                    this.style.background = 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)';
                    this.innerHTML = '<h4>🎯 CLICK ME!</h4><p>This is the smart click target</p><p>ID: smart-click-target</p>';
                }, 3000);
            });
        });
    </script>
</body>
</html>
