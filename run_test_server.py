#!/usr/bin/env python3
"""
Test Server Runner for ScreenMonitorMCP
Starts the MCP server and opens the test page
"""

import subprocess
import time
import webbrowser
import sys
import os

def main():
    print("🚀 Starting ScreenMonitorMCP Test Environment")
    print("=" * 60)
    
    # Change to project directory
    project_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(project_dir)
    
    print(f"📁 Working directory: {project_dir}")
    
    # Start MCP server in background
    print("🔧 Starting MCP Server...")
    try:
        # Start server process
        server_process = subprocess.Popen([
            sys.executable, "main.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print("⏳ Waiting for server to start...")
        time.sleep(3)
        
        # Check if server is still running
        if server_process.poll() is None:
            print("✅ MCP Server started successfully!")
            print("🌐 Server running on: http://localhost:7777")
            
            # Wait a bit more for full initialization
            time.sleep(2)
            
            # Open test page in browser
            test_page_path = os.path.join(project_dir, "test-page.html")
            test_page_url = f"file:///{test_page_path.replace(os.sep, '/')}"
            
            print(f"🧪 Opening test page: {test_page_url}")
            webbrowser.open(test_page_url)
            
            print("\n" + "=" * 60)
            print("🎉 TEST ENVIRONMENT READY!")
            print("\n📋 What to do next:")
            print("1. 🌐 Test page should open in your browser")
            print("2. 🧩 Install the Chrome extension from browser_extension/dist/")
            print("3. 🔌 Test MCP Server connection")
            print("4. 🎯 Test Smart Click features")
            print("5. 📡 Test DOM Event monitoring")
            print("6. 🔐 Test Session management")
            
            print("\n🛠️ Available endpoints:")
            print("  • Health: http://localhost:7777/api/health")
            print("  • Config: http://localhost:7777/api/extension/config")
            print("  • Register: http://localhost:7777/api/extension/register")
            
            print("\n⚠️  Press Ctrl+C to stop the server")
            print("=" * 60)
            
            # Keep server running
            try:
                server_process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Stopping server...")
                server_process.terminate()
                server_process.wait()
                print("✅ Server stopped successfully!")
                
        else:
            print("❌ Server failed to start!")
            stdout, stderr = server_process.communicate()
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return 1
            
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())