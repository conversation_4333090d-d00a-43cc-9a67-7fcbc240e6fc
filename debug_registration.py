import asyncio
import sys
sys.path.append('.')

async def debug_registration():
    try:
        from web_extension.extension_server import get_extension_server, initialize_extension_server
        from web_extension.models import RegistrationRequest

        print("Testing extension server directly...")

        # Initialize extension server first
        extension_server = initialize_extension_server()
        print("Extension server initialized:", extension_server)
        
        if extension_server:
            # Create registration request
            request = RegistrationRequest(
                domain='localhost',
                features=['dom_monitoring', 'smart_click'],
                session_id=None,
                user_agent='Test Browser MCP'
            )
            print("Request created:", request)
            
            # Register extension
            result = await extension_server.register_extension(request)
            print("Registration result:", result)
        else:
            print("Extension server not available")
            
    except Exception as e:
        print("Error:", str(e))
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_registration())