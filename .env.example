# ========================================
# ScreenMonitorMCP Environment Variables Configuration
# ========================================
# Bu dosyayı '.env' olarak yeniden adlandırın ve gerçek API anahtarlarınızı girin.
# Rename this file to '.env' and fill in your actual API keys for usage.

# ========================================
# SERVER CONFIGURATION / SUNUCU YAPILANDIRMASI
# ========================================

# Server API Key for securing endpoints / Endpoint güvenliği için sunucu API anahtarı
API_KEY=your_secret_server_key_here

# Server host and port configuration / Sunucu host ve port yapılandırması
HOST=127.0.0.1
PORT=7777

# ========================================
# AI MODEL CONFIGURATION / AI MODEL YAPILANDIRMASI
# ========================================

# OpenAI API Key for vision analysis / Görüntü analizi için OpenAI API anahtarı
OPENAI_API_KEY=sk-your-openai-api-key-here

# Custom OpenAI API Base URL / Özel OpenAI API Base URL
# Default: https://api.openai.com/v1 (OpenAI resmi endpoint)
# Alternative: https://openrouter.ai/api/v1 (OpenRouter için)
OPENAI_BASE_URL=https://openrouter.ai/api/v1

# Default AI Model for Analysis / Analiz için varsayılan AI modeli
# Önerilen modeller / Recommended models:
# - gpt-4o-mini (OpenAI - hızlı ve ekonomik)
# - gpt-4o (OpenAI - en gelişmiş)
# - mistralai/mistral-small-3.2-24b-instruct:free (OpenRouter - ücretsiz)
# - anthropic/claude-3-haiku (OpenRouter - hızlı)
DEFAULT_OPENAI_MODEL=mistralai/mistral-small-3.2-24b-instruct:free

# Default Max Tokens for AI Analysis / AI analizi için varsayılan maksimum token sayısı
# Minimum 1000 önerilir - AI modellerin esnek yanıt verebilmesi için
# Recommended minimum 1000 - for flexible AI model responses
DEFAULT_MAX_TOKENS=1000

# ========================================
# MONITORING CONFIGURATION / İZLEME YAPILANDIRMASI
# ========================================

# Continuous monitoring settings / Sürekli izleme ayarları
DEFAULT_FPS=2
DEFAULT_CHANGE_THRESHOLD=0.1
DEFAULT_MAJOR_CHANGE_THRESHOLD=0.3
DEFAULT_CRITICAL_CHANGE_THRESHOLD=0.6

# Screenshot storage / Ekran görüntüsü depolama
SAVE_SCREENSHOTS=true
SCREENSHOT_DIR=./screenshots

# ========================================
# ADVANCED FEATURES / GELİŞMİŞ ÖZELLİKLER
# ========================================

# Smart detection features / Akıllı algılama özellikleri
SMART_DETECTION=true
UI_ELEMENT_DETECTION=true
OCR_ENGINE=auto

# User behavior learning / Kullanıcı davranış öğrenme
ENABLE_USER_LEARNING=true
PREDICTION_HORIZON=30

# ========================================
# WEB EXTENSION / WEB UZANTISI
# ========================================

# Enable web extension support / Web uzantı desteğini etkinleştir
# This enables the web extension module and HTTP server for browser extension communication
ENABLE_WEB_EXTENSION=true

# Web extension allowed domains / Web uzantı izin verilen domainler
# Comma-separated list of allowed domains for web extension features
ALLOWED_DOMAINS=localhost,127.0.0.1,file,github.com,stackoverflow.com,docs.python.org,developer.mozilla.org

# Web extension API key / Web uzantı API anahtarı
WEB_EXTENSION_API_KEY=your_web_extension_api_key_here

# Web extension debug mode / Web uzantı debug modu
WEB_EXTENSION_DEBUG=true

# Web extension features / Web uzantı özellikleri
# Individual feature toggles for web extension functionality
FEATURE_WEB_MONITORING=true
FEATURE_DOM_DETECTION=true
FEATURE_WEB_SMART_CLICK=true
FEATURE_DOMAIN_WHITELIST=true
FEATURE_WEB_AUTOMATION=true
FEATURE_EVENT_ANALYSIS=true

# ========================================
# BROWSER EXTENSION / TARAYICI UZANTISI
# ========================================

# Enable modern browser extension support / Modern browser uzantı desteğini etkinleştir
# Set to 'true' to enable browser extension features with TypeScript and Manifest V3
ENABLE_BROWSER_EXTENSION=true

# Browser extension server configuration / Browser uzantı sunucu yapılandırması
BROWSER_EXTENSION_SERVER_URL=http://localhost:7777
BROWSER_EXTENSION_API_KEY=your_browser_extension_api_key_here

# Browser extension features / Browser uzantı özellikleri
# Available features: dom_monitoring, smart_click, text_extraction, event_analysis, page_monitoring
BROWSER_EXTENSION_FEATURES=dom_monitoring,smart_click,text_extraction,event_analysis,page_monitoring

# Domain whitelist for browser extension / Browser uzantı için domain beyaz listesi
# Comma-separated list of allowed domains (localhost, 127.0.0.1, and file are always included)
BROWSER_EXTENSION_ALLOWED_DOMAINS=localhost,127.0.0.1,file,github.com,stackoverflow.com,docs.python.org,developer.mozilla.org

# Browser extension security settings / Browser uzantı güvenlik ayarları
BROWSER_EXTENSION_ENABLE_CORS=true
BROWSER_EXTENSION_SECURE_MODE=false
BROWSER_EXTENSION_DEBUG_MODE=true

# Real-time communication settings / Gerçek zamanlı iletişim ayarları
BROWSER_EXTENSION_WEBSOCKET_ENABLED=false
BROWSER_EXTENSION_POLLING_INTERVAL=1000



# ========================================
# MCP SERVER INTEGRATION / MCP SUNUCU ENTEGRASYONU
# ========================================

# MCP server endpoints / MCP sunucu endpoint'leri
MCP_SERVER_STATUS_ENDPOINT=/api/status
MCP_SERVER_SMART_CLICK_ENDPOINT=/api/smart-click
MCP_SERVER_PAGE_INFO_ENDPOINT=/api/page-info
MCP_SERVER_DOM_EVENT_ENDPOINT=/api/dom-event

# MCP server response settings / MCP sunucu yanıt ayarları
MCP_SERVER_TIMEOUT=5000
MCP_SERVER_RETRY_ATTEMPTS=3
MCP_SERVER_RETRY_DELAY=1000

# MCP server security / MCP sunucu güvenliği
MCP_SERVER_REQUIRE_AUTH=true
MCP_SERVER_CORS_ORIGINS=chrome-extension://,moz-extension://,http://localhost,https://localhost

# ========================================
# BROWSER EXTENSION BUILD / TARAYICI UZANTISI DERLEME
# ========================================

# Build configuration / Derleme yapılandırması
BROWSER_EXTENSION_BUILD_MODE=production
BROWSER_EXTENSION_SOURCE_MAPS=false
BROWSER_EXTENSION_MINIFY=true

# Development settings / Geliştirme ayarları
BROWSER_EXTENSION_DEV_MODE=false
BROWSER_EXTENSION_HOT_RELOAD=false
BROWSER_EXTENSION_WATCH_MODE=false

# Extension manifest settings / Uzantı manifest ayarları
BROWSER_EXTENSION_MANIFEST_VERSION=3
BROWSER_EXTENSION_NAME=ScreenMonitorMCP Web Extension
BROWSER_EXTENSION_VERSION=2.0.0
BROWSER_EXTENSION_DESCRIPTION=Modern browser extension for ScreenMonitorMCP - enables AI assistants to monitor and interact with web pages securely

# ========================================
# LOGGING & DEBUG / GÜNLÜK KAYDI VE HATA AYIKLAMA
# ========================================

# Log level: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# Enable detailed logging / Detaylı günlük kaydını etkinleştir
ENABLE_DEBUG_LOGGING=false

# Browser extension specific logging / Browser uzantı özel günlük kaydı
BROWSER_EXTENSION_LOG_LEVEL=INFO
BROWSER_EXTENSION_CONSOLE_LOGGING=true
BROWSER_EXTENSION_FILE_LOGGING=false

# ========================================
# USAGE INSTRUCTIONS / KULLANIM TALİMATLARI
# ========================================

# 1. SETUP / KURULUM:
#    - Bu dosyayı '.env' olarak kopyalayın / Copy this file as '.env'
#    - API anahtarlarınızı girin / Fill in your API keys
#    - Web extension'ı etkinleştirin: ENABLE_WEB_EXTENSION=true / Enable web extension: ENABLE_WEB_EXTENSION=true
#    - MCP server'ı başlatın: python main.py / Start MCP server: python main.py
#
# 2. BROWSER EXTENSION SETUP / TARAYICI UZANTISI KURULUM:
#    - cd browser_extension
#    - npm install
#    - npm run build
#    - Chrome'da chrome://extensions/ sayfasına gidin / Go to chrome://extensions/ in Chrome
#    - "Developer mode" etkinleştirin / Enable "Developer mode"
#    - "Load unpacked" tıklayın ve 'dist' klasörünü seçin / Click "Load unpacked" and select 'dist' folder
#
# 3. TESTING / TEST:
#    - Uzantı ikonuna tıklayın / Click extension icon
#    - "Test Connection" butonuna tıklayın / Click "Test Connection" button
#    - MCP server bağlantısını doğrulayın / Verify MCP server connection
#
# 4. FEATURES / ÖZELLİKLER:
#    - ✅ Smart Click: Elementleri otomatik bulup tıklama
#    - ✅ DOM Monitoring: Sayfa değişikliklerini izleme
#    - ✅ Real-time Communication: MCP server ile gerçek zamanlı iletişim
#    - ✅ Page Analysis: Sayfa içeriği analizi
#    - ✅ Event Tracking: Kullanıcı etkileşimlerini izleme
#
# 5. TROUBLESHOOTING / SORUN GİDERME:
#    - MCP server çalışıyor mu kontrol edin / Check if MCP server is running
#    - ENABLE_WEB_EXTENSION=true olduğunu kontrol edin / Check ENABLE_WEB_EXTENSION=true
#    - Port 7777 açık mı kontrol edin / Check if port 7777 is open
#    - HTTP server başladı mı kontrol edin / Check if HTTP server started
#    - Browser console'da hata mesajları kontrol edin / Check browser console for errors
#    - Uzantı permissions'ları kontrol edin / Check extension permissions
#    - Domain whitelist'te siteniz var mı kontrol edin / Check if your domain is in whitelist
