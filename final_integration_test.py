#!/usr/bin/env python3
"""
Final Integration Test for ScreenMonitorMCP Web Extension
Tests all MCP tools and HTTP endpoints
"""

import asyncio
import sys
import json
import requests
import time
sys.path.append('.')

async def test_mcp_tools():
    """Test all MCP tools"""
    print("🔧 Testing MCP Tools...")
    
    from main import (get_web_extension_config, register_browser_extension, 
                     get_browser_session_info, send_dom_event, execute_smart_click_web)
    
    # Test 1: Get config
    config_result = await get_web_extension_config()
    assert config_result.get('success'), "Config test failed"
    print("✅ get_web_extension_config - PASSED")
    
    # Test 2: Register extension
    register_result = await register_browser_extension(
        domain='localhost',
        features=['dom_monitoring', 'smart_click'],
        user_agent='Final Test Browser'
    )
    assert register_result.get('success'), "Registration failed"
    session_id = register_result.get('data', {}).get('session_id')
    assert session_id, "No session ID returned"
    print(f"✅ register_browser_extension - PASSED (Session: {session_id})")
    
    # Test 3: Get session info
    session_result = await get_browser_session_info(session_id)
    assert session_result.get('success'), "Session info failed"
    print("✅ get_browser_session_info - PASSED")
    
    # Test 4: Get all sessions
    all_sessions_result = await get_browser_session_info()
    assert all_sessions_result.get('success'), "All sessions failed"
    total_sessions = all_sessions_result.get('data', {}).get('total_sessions', 0)
    assert total_sessions > 0, "No sessions found"
    print(f"✅ get_browser_session_info (all) - PASSED ({total_sessions} sessions)")
    
    return session_id

def test_http_endpoints(session_id):
    """Test HTTP endpoints"""
    print("\n🌐 Testing HTTP Endpoints...")
    
    base_url = "http://localhost:7777"
    
    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/api/health", timeout=5)
        assert response.status_code == 200, f"Health check failed: {response.status_code}"
        health_data = response.json()
        assert health_data.get('status') == 'healthy', "Server not healthy"
        print("✅ GET /api/health - PASSED")
    except Exception as e:
        print(f"❌ GET /api/health - FAILED: {e}")
        return False
    
    # Test config endpoint
    try:
        response = requests.get(f"{base_url}/api/extension/config", timeout=5)
        assert response.status_code == 200, f"Config failed: {response.status_code}"
        config_data = response.json()
        assert config_data.get('success'), "Config not successful"
        print("✅ GET /api/extension/config - PASSED")
    except Exception as e:
        print(f"❌ GET /api/extension/config - FAILED: {e}")
        return False
    
    # Test registration endpoint
    try:
        registration_data = {
            "domain": "localhost",
            "features": ["dom_monitoring", "smart_click"],
            "user_agent": "HTTP Test Browser"
        }
        response = requests.post(f"{base_url}/api/extension/register", 
                               json=registration_data, timeout=5)
        assert response.status_code == 200, f"Registration failed: {response.status_code}"
        reg_data = response.json()
        assert reg_data.get('success'), "Registration not successful"
        http_session_id = reg_data.get('data', {}).get('session_id')
        assert http_session_id, "No session ID from HTTP registration"
        print(f"✅ POST /api/extension/register - PASSED (Session: {http_session_id})")
    except Exception as e:
        print(f"❌ POST /api/extension/register - FAILED: {e}")
        return False
    
    return True

async def main():
    """Main test function"""
    print("🚀 ScreenMonitorMCP Web Extension Integration Test")
    print("=" * 60)
    
    try:
        # Test MCP tools
        session_id = await test_mcp_tools()
        
        # Wait a moment for HTTP server to be ready
        print("\n⏳ Waiting for HTTP server...")
        time.sleep(2)
        
        # Test HTTP endpoints
        http_success = test_http_endpoints(session_id)
        
        print("\n" + "=" * 60)
        if http_success:
            print("🎉 ALL TESTS PASSED! Web Extension MCP Integration is working!")
            print("\n📋 Summary:")
            print("✅ MCP Tools: All 4 tools working")
            print("✅ HTTP Endpoints: All 3 endpoints working")
            print("✅ Session Management: Working")
            print("✅ Extension Server: Initialized and running")
            print("✅ Browser Extension Support: Ready")
            
            print("\n🔗 Available MCP Tools for Claude Desktop:")
            print("  • get_web_extension_config()")
            print("  • register_browser_extension(domain, features, user_agent)")
            print("  • get_browser_session_info(session_id?)")
            print("  • send_dom_event(session_id, event_type, event_data)")
            print("  • execute_smart_click_web(session_id, element_description)")
            
            print("\n🌐 Available HTTP Endpoints for Browser Extension:")
            print("  • GET  /api/health")
            print("  • GET  /api/extension/config")
            print("  • POST /api/extension/register")
            print("  • POST /api/extension/dom-event")
            print("  • POST /api/extension/smart-click")
            
            return True
        else:
            print("❌ SOME TESTS FAILED! Check the errors above.")
            return False
            
    except Exception as e:
        print(f"\n❌ CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)