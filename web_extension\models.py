"""
Web Extension Data Models

Type-safe data models for web extension server with validation,
serialization, and proper error handling.
"""

from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime
from enum import Enum
import json
import uuid

# ============================================================================
# Enums
# ============================================================================

class EventType(Enum):
    """DOM event types"""
    PAGE_LOAD = "page_load"
    DOM_CHANGE = "dom_change"
    CLICK = "click"
    FORM_SUBMIT = "form_submit"
    NAVIGATION_CHANGE = "navigation_change"
    BUTTON_CLICK = "button_click"
    INPUT_CHANGE = "input_change"
    SCROLL = "scroll"
    RESIZE = "resize"

class SessionStatus(Enum):
    """Session status types"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"
    TERMINATED = "terminated"

class ResponseStatus(Enum):
    """API response status"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"

# ============================================================================
# Base Models
# ============================================================================

@dataclass
class BaseModel:
    """Base model with common functionality"""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), default=str)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create instance from dictionary"""
        return cls(**data)

# ============================================================================
# Session Models
# ============================================================================

@dataclass
class SessionInfo(BaseModel):
    """Browser extension session information"""
    session_id: str
    domain: str
    url: str
    features: List[str] = field(default_factory=list)
    user_agent: str = ""
    registered_at: str = field(default_factory=lambda: datetime.now().isoformat())
    last_activity: str = field(default_factory=lambda: datetime.now().isoformat())
    status: SessionStatus = SessionStatus.ACTIVE
    tab_id: Optional[int] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate and normalize data after initialization"""
        if not self.session_id:
            self.session_id = self.generate_session_id()
        
        # Normalize domain
        self.domain = self.domain.lower().strip()
        
        # Ensure timestamps are ISO format
        if isinstance(self.registered_at, datetime):
            self.registered_at = self.registered_at.isoformat()
        if isinstance(self.last_activity, datetime):
            self.last_activity = self.last_activity.isoformat()
    
    @staticmethod
    def generate_session_id() -> str:
        """Generate a unique session ID"""
        return f"session_{uuid.uuid4().hex[:16]}"
    
    def update_activity(self) -> None:
        """Update last activity timestamp"""
        self.last_activity = datetime.now().isoformat()
    
    def is_active(self, timeout_minutes: int = 30) -> bool:
        """Check if session is still active based on last activity"""
        if self.status != SessionStatus.ACTIVE:
            return False
        
        try:
            last_activity = datetime.fromisoformat(self.last_activity)
            timeout_delta = datetime.now() - last_activity
            return timeout_delta.total_seconds() < (timeout_minutes * 60)
        except (ValueError, TypeError):
            return False
    
    def expire(self) -> None:
        """Mark session as expired"""
        self.status = SessionStatus.EXPIRED
        self.update_activity()

# ============================================================================
# Event Models
# ============================================================================

@dataclass
class ElementInfo(BaseModel):
    """Information about a DOM element"""
    tag_name: str
    id: Optional[str] = None
    class_name: Optional[str] = None
    text_content: Optional[str] = None
    selector: str = ""
    attributes: Dict[str, str] = field(default_factory=dict)
    bounding_rect: Optional[Dict[str, float]] = None
    
    def __post_init__(self):
        """Validate and normalize element info"""
        self.tag_name = self.tag_name.upper()
        
        # Truncate text content if too long
        if self.text_content and len(self.text_content) > 200:
            self.text_content = self.text_content[:200] + "..."

@dataclass
class Coordinates(BaseModel):
    """Screen/element coordinates"""
    x: float
    y: float
    
    def __post_init__(self):
        """Validate coordinates"""
        if not isinstance(self.x, (int, float)) or not isinstance(self.y, (int, float)):
            raise ValueError("Coordinates must be numeric")

@dataclass
class MutationInfo(BaseModel):
    """Information about a DOM mutation"""
    type: str
    target: ElementInfo
    added_nodes: int = 0
    removed_nodes: int = 0
    attribute_name: Optional[str] = None
    old_value: Optional[str] = None

@dataclass
class DOMEvent(BaseModel):
    """DOM event data"""
    event_type: EventType
    url: str
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    target: Optional[ElementInfo] = None
    mutations: List[MutationInfo] = field(default_factory=list)
    coordinates: Optional[Coordinates] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate and normalize event data"""
        # Ensure event_type is EventType enum
        if isinstance(self.event_type, str):
            try:
                self.event_type = EventType(self.event_type)
            except ValueError:
                raise ValueError(f"Invalid event type: {self.event_type}")
        
        # Validate URL
        if not self.url or not isinstance(self.url, str):
            raise ValueError("URL is required and must be a string")
        
        # Ensure timestamp is ISO format
        if isinstance(self.timestamp, datetime):
            self.timestamp = self.timestamp.isoformat()

# ============================================================================
# Smart Click Models
# ============================================================================

@dataclass
class SmartClickRequest(BaseModel):
    """Smart click request data"""
    session_id: str
    element_description: str
    css_selector: Optional[str] = None
    confidence_threshold: float = 0.8
    timeout: int = 5000
    dry_run: bool = False
    
    def __post_init__(self):
        """Validate smart click request"""
        if not self.session_id:
            raise ValueError("Session ID is required")
        
        if not self.element_description:
            raise ValueError("Element description is required")
        
        if not 0.0 <= self.confidence_threshold <= 1.0:
            raise ValueError("Confidence threshold must be between 0.0 and 1.0")
        
        if self.timeout <= 0:
            raise ValueError("Timeout must be positive")

@dataclass
class SmartClickResult(BaseModel):
    """Smart click execution result"""
    success: bool
    element_found: bool
    element: Optional[ElementInfo] = None
    coordinates: Optional[Coordinates] = None
    confidence: float = 0.0
    error: Optional[str] = None
    execution_time: float = 0.0
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def __post_init__(self):
        """Validate smart click result"""
        if not 0.0 <= self.confidence <= 1.0:
            self.confidence = max(0.0, min(1.0, self.confidence))
        
        if self.execution_time < 0:
            self.execution_time = 0.0

# ============================================================================
# API Response Models
# ============================================================================

@dataclass
class APIResponse(BaseModel):
    """Standard API response format"""
    success: bool
    status: ResponseStatus = ResponseStatus.SUCCESS
    data: Optional[Any] = None
    error: Optional[str] = None
    message: Optional[str] = None
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    request_id: Optional[str] = None
    
    def __post_init__(self):
        """Set status based on success if not explicitly set"""
        if not self.success and self.status == ResponseStatus.SUCCESS:
            self.status = ResponseStatus.ERROR

@dataclass
class RegistrationResponse(APIResponse):
    """Extension registration response"""
    session_id: Optional[str] = None
    enabled_features: List[str] = field(default_factory=list)
    server_version: str = "2.0.0"
    allowed_domains: List[str] = field(default_factory=list)

@dataclass
class ConfigResponse(APIResponse):
    """Configuration response"""
    config: Dict[str, Any] = field(default_factory=dict)
    allowed_domains: List[str] = field(default_factory=list)
    enabled_features: List[str] = field(default_factory=list)
    active_sessions: int = 0

# ============================================================================
# Validation Models
# ============================================================================

@dataclass
class ValidationResult(BaseModel):
    """Validation result"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    def add_error(self, error: str) -> None:
        """Add validation error"""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str) -> None:
        """Add validation warning"""
        self.warnings.append(warning)

# ============================================================================
# Request Models
# ============================================================================

@dataclass
class RegistrationRequest(BaseModel):
    """Extension registration request"""
    domain: str
    features: List[str] = field(default_factory=list)
    user_agent: str = ""
    session_id: Optional[str] = None
    tab_id: Optional[int] = None
    url: Optional[str] = None
    
    def __post_init__(self):
        """Validate registration request"""
        if not self.domain:
            raise ValueError("Domain is required")
        
        # Generate session ID if not provided
        if not self.session_id:
            self.session_id = SessionInfo.generate_session_id()
        
        # Normalize domain
        self.domain = self.domain.lower().strip()

@dataclass
class DOMEventRequest(BaseModel):
    """DOM event request"""
    session_id: str
    event_type: str
    event_data: Dict[str, Any]
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def __post_init__(self):
        """Validate DOM event request"""
        if not self.session_id:
            raise ValueError("Session ID is required")
        
        if not self.event_type:
            raise ValueError("Event type is required")
        
        if not self.event_data:
            raise ValueError("Event data is required")

# ============================================================================
# Statistics Models
# ============================================================================

@dataclass
class SessionStats(BaseModel):
    """Session statistics"""
    total_sessions: int = 0
    active_sessions: int = 0
    expired_sessions: int = 0
    domains: List[str] = field(default_factory=list)
    features_usage: Dict[str, int] = field(default_factory=dict)
    events_processed: int = 0
    
@dataclass
class ServerStats(BaseModel):
    """Server statistics"""
    uptime: float = 0.0
    version: str = "2.0.0"
    sessions: SessionStats = field(default_factory=SessionStats)
    memory_usage: Dict[str, Any] = field(default_factory=dict)
    performance: Dict[str, float] = field(default_factory=dict)

# ============================================================================
# Utility Functions
# ============================================================================

def validate_model_data(model_class, data: Dict[str, Any]) -> ValidationResult:
    """Validate data against a model class"""
    result = ValidationResult(is_valid=True)
    
    try:
        # Try to create instance
        model_class.from_dict(data)
    except (TypeError, ValueError) as e:
        result.add_error(f"Model validation failed: {str(e)}")
    except Exception as e:
        result.add_error(f"Unexpected validation error: {str(e)}")
    
    return result

def serialize_model(model: BaseModel) -> str:
    """Serialize model to JSON string"""
    try:
        return model.to_json()
    except Exception as e:
        raise ValueError(f"Serialization failed: {str(e)}")

def deserialize_model(model_class, json_str: str) -> BaseModel:
    """Deserialize JSON string to model"""
    try:
        data = json.loads(json_str)
        return model_class.from_dict(data)
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON: {str(e)}")
    except Exception as e:
        raise ValueError(f"Deserialization failed: {str(e)}")

# ============================================================================
# Export All Models
# ============================================================================

__all__ = [
    # Enums
    'EventType',
    'SessionStatus', 
    'ResponseStatus',
    
    # Base
    'BaseModel',
    
    # Session models
    'SessionInfo',
    
    # Event models
    'ElementInfo',
    'Coordinates',
    'MutationInfo',
    'DOMEvent',
    
    # Smart click models
    'SmartClickRequest',
    'SmartClickResult',
    
    # API models
    'APIResponse',
    'RegistrationResponse',
    'ConfigResponse',
    
    # Validation
    'ValidationResult',
    
    # Request models
    'RegistrationRequest',
    'DOMEventRequest',
    
    # Statistics
    'SessionStats',
    'ServerStats',
    
    # Utilities
    'validate_model_data',
    'serialize_model',
    'deserialize_model',
]