import asyncio
import sys
sys.path.append('.')

async def test_all_mcp_tools():
    from main import (get_web_extension_config, register_browser_extension, 
                     get_browser_session_info, send_dom_event, execute_smart_click_web)
    
    print('=== Testing All Web Extension MCP Tools ===')
    
    # Test 1: Get web extension config
    print('\n1. Testing get_web_extension_config...')
    config_result = await get_web_extension_config()
    print('Config loaded successfully')
    
    # Test 2: Register browser extension
    print('\n2. Testing register_browser_extension...')
    register_result = await register_browser_extension(
        domain='localhost',
        features=['dom_monitoring', 'smart_click'],
        user_agent='Test Browser MCP'
    )
    print('Register success:', register_result.get('success'))
    session_id = register_result.get('data', {}).get('session_id')
    print('Session ID:', session_id)
    
    if session_id:
        # Test 3: Send DOM event
        print('\n3. Testing send_dom_event...')
        dom_result = await send_dom_event(
            session_id=session_id,
            event_type='click',
            event_data={
                'element': {'tag': 'button', 'id': 'test-btn'},
                'coordinates': {'x': 100, 'y': 200}
            }
        )
        print('DOM event success:', dom_result.get('success'))
        
        # Test 4: Get session info
        print('\n4. Testing get_browser_session_info...')
        session_result = await get_browser_session_info(session_id)
        print('Session info success:', session_result.get('success'))
        
        # Test 5: Get all sessions
        print('\n5. Testing get_browser_session_info (all sessions)...')
        all_sessions_result = await get_browser_session_info()
        print('All sessions success:', all_sessions_result.get('success'))
        print('Total sessions:', all_sessions_result.get('data', {}).get('total_sessions', 0))
    
    print('\nAll MCP Tools Tests Completed Successfully!')

if __name__ == "__main__":
    asyncio.run(test_all_mcp_tools())